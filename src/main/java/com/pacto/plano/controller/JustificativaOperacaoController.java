package com.pacto.plano.controller;


import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.config.swagger.respostas.justificativaoperacao.EnvelopeRespostaJustificativaOperacao;
import com.pacto.plano.enumerators.TipoOperacao;
import com.pacto.plano.services.interfaces.JustificativaOperacaoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.pacto.plano.config.swagger.SwaggerTags.JUSTIFICATIVA_OPERACAO;
import static com.pacto.plano.config.swagger.respostas.justificativaoperacao.EnvelopeRespostaJustificativaOperacao.JUSTIFICATIVA_OPERACAO_RESPOSTA_LIST;

@RestController
@RequestMapping("/justificativa-operacao")
@Tag(name = JUSTIFICATIVA_OPERACAO)
public class JustificativaOperacaoController {

    private final JustificativaOperacaoService justificativaOperacaoService;

    public JustificativaOperacaoController(JustificativaOperacaoService justificativaOperacaoService) {
        this.justificativaOperacaoService = justificativaOperacaoService;
    }

    @Operation(
            summary = "Consultar justificativas de operação",
            description = "Consulta as justificativas de operação.",
            parameters = {
                    @Parameter(name = "tipoOperacao", description = "Filtra pelo tipo de operação<br/>" +
                            "<strong>Valores disponíveis</strong>" +
                            "<ul>" +
                            "<li>RE (Redução)</li>" +
                            "<li>AC (Acréscimo)</li>" +
                            "<li>EX (Exatamente)</li>" +
                            "</ul>", example = "RE", schema = @Schema(implementation = TipoOperacao.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaJustificativaOperacao.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = JUSTIFICATIVA_OPERACAO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping({"", "/{tipoOperacao}"})
    public ResponseEntity<EnvelopeRespostaDTO> getJustificativaOperacaobyEmpresa(@PathVariable(required = false) String tipoOperacao) {
        try {
            return ResponseEntityFactory.ok(justificativaOperacaoService.findByTipoOperacao(tipoOperacao));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }
}
