package com.pacto.plano.controller;

import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.config.swagger.respostas.modalidade.EnvelopeRespostaListModalidade;
import com.pacto.plano.config.swagger.respostas.modalidade.EnvelopeRespostaModalidade;
import com.pacto.plano.dto.filtros.FiltroModalidadeJSON;
import com.pacto.plano.dto.modalidade.ModalidadeDTO;
import com.pacto.plano.services.interfaces.ModalidadeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

import static com.pacto.plano.config.swagger.SwaggerTags.MODALIDADE;


@RestController
@RequestMapping("/modalidades")
@Tag(name = MODALIDADE)
public class ModalidadeController {

    private final ModalidadeService modalidadeService;

    public ModalidadeController(ModalidadeService modalidadeService) {
        this.modalidadeService = modalidadeService;
    }


    @Operation(
            summary = "Consultar nome e código das modalidades",
            description = "Consulta apenas os nomes e códigos das modalidades podendo aplicar filtros.",
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa", required = true, example = "1", in = ParameterIn.HEADER),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pelo nome da modalidade.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"QUINTA\"}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListModalidade.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListModalidade.MODALIDADE_NOME_CODIGO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/only-cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findAllCodName(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroModalidadeJSON filtroModalidadeJSON = new FiltroModalidadeJSON(filtros);
            return ResponseEntityFactory.ok(modalidadeService.findAllCodName(filtroModalidadeJSON));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Cadastrar modalidade",
            description = "Cadastra uma modalidade.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ModalidadeDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaModalidade.MODALIDADE_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaModalidade.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaModalidade.MODALIDADE_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> save(@RequestBody ModalidadeDTO modalidadeDTO) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.save(modalidadeDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Deletar uma modalidade",
            description = "Deleta uma modalidade pelo código informado.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da modalidade que será deletada", required = true, example = "1")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. Não retorna corpo de resposta.",
                                                    value = ""),
                                    }
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarModalidade(@PathVariable Collection id) {
        try {
            modalidadeService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar modalidade",
            description = "Consulta uma modalidade pelo código identificador dela.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da modalidade que será consultada", required = true, example = "1")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaModalidade.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaModalidade.MODALIDADE_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(modalidadeService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
