package com.pacto.plano.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.config.swagger.respostas.conveniodesconto.EnvelopeRespostaConvenioDesconto;
import com.pacto.plano.config.swagger.respostas.conveniodesconto.EnvelopeRespostaListConvenioDesconto;
import com.pacto.plano.dto.convenioDesconto.ConvenioDescontoDTO;
import com.pacto.plano.dto.filtros.FiltroConvenioDescontoJSON;
import com.pacto.plano.services.interfaces.ConvenioDescontoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.pacto.plano.config.swagger.SwaggerTags.CONVENIO_DESCONTO;
import static com.pacto.plano.config.swagger.respostas.conveniodesconto.EnvelopeRespostaConvenioDesconto.CONVENIO_DESCONTO_ATRIBUTOS;
import static com.pacto.plano.config.swagger.respostas.conveniodesconto.EnvelopeRespostaConvenioDesconto.CONVENIO_DESCONTO_RESPOSTA;
import static com.pacto.plano.config.swagger.respostas.conveniodesconto.EnvelopeRespostaListConvenioDesconto.CONVENIO_DESCONTO_RESPOSTA_LIST;

@RestController
@RequestMapping("/convenio-desconto")
@Tag(name = CONVENIO_DESCONTO)
public class ConvenioDescontoController {

    @Autowired
    ConvenioDescontoService convenioDescontoService;


    @Operation(
            summary = "Consultar todos os convênios de desconto",
            description = "Consulta todos os convênios de desconto podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição do convênio, pelo username do usuário responsável, pelo nome do usuário responsável ou pelo código do convênio de desconto.</li>" +
                                    "<li><strong>dataInicioVigencia:</strong> Filtra pela data de início da vigência do convênio de desconto.</li>" +
                                    "<li><strong>dataFinalVigencia:</strong> Filtra pela data final de vigência do convênio de desconto.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"convênio\",\"dataInicioVigencia\":\"2024-10-01\",\"dataFinalVigencia\":\"2024-12-31\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListConvenioDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = CONVENIO_DESCONTO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroConvenioDescontoJSON filtroConvenioDescontoJSON = new FiltroConvenioDescontoJSON(filtros);
            return ResponseEntityFactory.ok(convenioDescontoService.findAllByEmpresa(filtroConvenioDescontoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar convênio de desconto",
            description = "Consulta um convênio de desconto pelo código informado.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do convênio de desconto qe será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConvenioDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = CONVENIO_DESCONTO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> convenioDesconto(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(convenioDescontoService.convenioDesconto(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Cadastrar um convênio de desconto",
            description = "Cadastra / inclui um novo convênio de desconto.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConvenioDescontoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = CONVENIO_DESCONTO_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaConvenioDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = CONVENIO_DESCONTO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirConvenioDesconto(@RequestBody ConvenioDescontoDTO convenioDescontoDTO) {
        try {
            return ResponseEntityFactory.ok(convenioDescontoService.saveOrUpdate(convenioDescontoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Deletar um convênio de desconto",
            description = "Deleta um convênio de desconto pelo id informado.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do convênio de desconto", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. Não retorna corpo de resposta.",
                                                    value = ""),
                                    }
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarConvenioDesconto(@PathVariable Integer id) {
        try {
            convenioDescontoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
