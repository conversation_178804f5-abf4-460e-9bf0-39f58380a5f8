package com.pacto.plano.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.adapters.modalidade.PacoteAdapter;
import com.pacto.plano.config.swagger.SwaggerTags;
import com.pacto.plano.config.swagger.respostas.pacote.EnvelopeRespostaListPacote;
import com.pacto.plano.config.swagger.respostas.pacote.EnvelopeRespostaPacote;
import com.pacto.plano.dto.filtros.FiltroPacoteJSON;
import com.pacto.plano.dto.modalidade.PacoteDTO;
import com.pacto.plano.services.interfaces.PacoteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;



@RestController
@RequestMapping("/pacotes")
@Tag(name = SwaggerTags.PACOTE)
public class PacoteController {

    private final PacoteService pacoteService;
    private final PacoteAdapter pacoteAdapter;
    private final RequestService requestService;

    public PacoteController(PacoteService pacoteService, PacoteAdapter pacoteAdapter, RequestService requestService) {
        this.pacoteService = pacoteService;
        this.pacoteAdapter = pacoteAdapter;
        this.requestService = requestService;
    }

    @Operation(
            summary = "Consultar pacotes",
            description = "Consulta os pacotes de planos da academia podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa que será utilizado na operação",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pelo descrição ou pelo código identificador do pacote.</li>" +
                                    "<li><strong>codigosNaoConsultar:</strong> Remove da busca os códigos dos pacotes que não devem ser buscados. (Deve ser informado como uma lista contendo os códigos dos planos)</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"nado livre\",\"codigosNaoConsultar\":[1,2,3] }",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPacote.PACOTE_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroPacoteJSON filtroPacoteJSON = new FiltroPacoteJSON(filtros);
            return ResponseEntityFactory.ok(pacoteService.findAll(filtroPacoteJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Cadastrar pacote",
            description = "Cadastra um novo pacote.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = PacoteDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaPacote.PACOTE_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPacote.PACOTE_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> save(@RequestBody PacoteDTO pacotes) {
        try {
            return ResponseEntityFactory.ok(pacoteService.save(pacotes));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar nome e códigos dos pacotes",
            description = "Consulta os nomes (descrição) e códigos dos pacotes de planos da academia podendo realizar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pelo descrição ou pelo código identificador do pacote.</li>" +
                                    "<li><strong>codigosNaoConsultar:</strong> Remove da busca os códigos dos pacotes que não devem ser buscados. (Deve ser informado como uma lista contendo os códigos dos planos)</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"nado livre\",\"codigosNaoConsultar\":[1,2,3] }",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaListPacote.PACOTE_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/only-cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findAllCodName(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroPacoteJSON filtroPacoteJSON = new FiltroPacoteJSON(filtros);
            return ResponseEntityFactory.ok(pacoteService.findAllCodName(filtroPacoteJSON));
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar pacote",
            description = "Consulta as informações de um pacote pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do pacote que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaPacote.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Resposta com status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = EnvelopeRespostaPacote.PACOTE_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> pacote(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(pacoteService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Deletar pacote",
            description = "Deleta as informações de um pacote pelo código identificador dele.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do pacote que será deletado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200 (Não retorna corpo de resposta)", summary = "Exemplo Status 200 (Não retorna corpo de resposta)",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = ""
                                            )
                                    }
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id) {
        try {
            pacoteService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
