package com.pacto.plano.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import com.pacto.plano.config.swagger.respostas.desconto.EnvelopeRespostaDesconto;
import com.pacto.plano.config.swagger.respostas.desconto.EnvelopeRespostaListDesconto;
import com.pacto.plano.dto.DescontoDTO;
import com.pacto.plano.dto.filtros.FiltroDescontoJSON;
import com.pacto.plano.services.interfaces.DescontoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.pacto.plano.config.swagger.SwaggerTags.DESCONTO;
import static com.pacto.plano.config.swagger.respostas.desconto.EnvelopeRespostaDesconto.DESCONTO_ATRIBUTOS;
import static com.pacto.plano.config.swagger.respostas.desconto.EnvelopeRespostaDesconto.DESCONTO_RESPOSTA;
import static com.pacto.plano.config.swagger.respostas.desconto.EnvelopeRespostaListDesconto.DESCONTO_RESPOSTA_LIST;

@RestController
@RequestMapping("/desconto")
@Tag(name = DESCONTO)
public class DescontoController {

    @Autowired
    DescontoService descontoService;

    @Operation(
            summary = "Cadastrar desconto",
            description = "Cadastra um novo desconto.",
            tags = {DESCONTO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar um novo registro. <strong>É necessário enviar sem o atributo codigo para criar</strong>",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = DescontoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = DESCONTO_ATRIBUTOS)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = DESCONTO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirDesconto(@RequestBody DescontoDTO descontos) {
        try {
            return ResponseEntityFactory.ok(descontoService.saveOrUpdate(descontos));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar todos os descontos",
            description = "Consulta todos os descontos podendo aplicar filtros durante a busca.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca. Deve ser informado como um JSON e realizado o ENCODE para realizar a requisição!<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>quicksearchValue:</strong> Filtra pela descrição do desconto ou pelo código identificador da desconto.</li>" +
                                    "</ul>",
                            example = "{\"quicksearchValue\":\"MATRÍCULA\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.<br/>" +
                                    "<strong>Ordens disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>asc:</strong> Ordena de forma ascendente pelo atributo definido</li>" +
                                    "<li><strong>desc:</strong> Ordena de forma descendente pelo atributo definido</li>" +
                                    "</ul>" +
                                    "Para fazer a ordenação, utilize o padrão: <strong>atributo,ordem</strong>.",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = DESCONTO_RESPOSTA_LIST
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {

        try {
            FiltroDescontoJSON filtroDescontoJSON = new FiltroDescontoJSON(filtros);
            return ResponseEntityFactory.ok(descontoService.findAll(filtroDescontoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar desconto",
            description = "Consulta um desconto pelo código identificador informado.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do desconto que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaDesconto.class),
                                    examples = {
                                            @ExampleObject(
                                                    name = "Exemplo Status 200", summary = "Exemplo Status 200",
                                                    description = "Exemplo de uma resposta com status 200 que retorna os dados solicitados",
                                                    value = DESCONTO_RESPOSTA
                                            )
                                    }
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> desconto(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(descontoService.desconto(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Deletar desconto",
            description = "Deleta um desconto pelo id informado.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do desconto que será deletado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida (Não retorna corpo de resposta)",
                            content = @Content(
                                    examples = {
                                            @ExampleObject(
                                                    summary = "Resposta Status 200",
                                                    description = "Exemplo de resposta com o status 200. Não retorna corpo de resposta.",
                                                    value = ""),
                                    }
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id) {
        try {
            descontoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
